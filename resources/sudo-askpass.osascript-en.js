#!/usr/bin/env osascript -l JavaScript

ObjC.import('stdlib')

const app = Application.currentApplication()
app.includeStandardAdditions = true

const result = app.displayDialog('b<PERSON><PERSON><PERSON><PERSON><PERSON> needs privileged access in order to flash disks.\n\nType your password to allow this.', {
  defaultAnswer: '',
  withIcon: 'caution',
  buttons: ['Cancel', 'Ok'],
  defaultButton: 'Ok',
  hiddenAnswer: true,
})

if (result.buttonReturned === 'Ok') {
  result.textReturned
} else {
  $.exit(255)
}

